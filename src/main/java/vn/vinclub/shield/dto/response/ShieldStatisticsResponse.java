package vn.vinclub.shield.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vinclub.shield.enums.StatisticTimePeriod;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Response DTO for shield protection statistics
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ShieldStatisticsResponse {
    
    /**
     * Time period for these statistics
     */
    private StatisticTimePeriod timePeriod;
    
    /**
     * Timestamp when statistics were retrieved
     */
    private LocalDateTime retrievedAt;
    
    /**
     * Core metrics
     */
    private CoreMetrics coreMetrics;
    
    /**
     * Risk category breakdown
     */
    private RiskCategoryMetrics riskCategoryMetrics;
    
    /**
     * Raw metrics map for additional data
     */
    private Map<String, Long> additionalMetrics;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class CoreMetrics {
        private Long totalRequests;
        private Long evaluationPass;
        private Long protectionRequired;
        private Long shieldVerificationSuccess;
        private Long shieldVerificationFailed;
        
        /**
         * Calculate protection rate as percentage
         * @return protection rate (0-100)
         */
        public Double getProtectionRate() {
            if (totalRequests == null || totalRequests == 0) {
                return 0.0;
            }
            return (protectionRequired != null ? protectionRequired : 0) * 100.0 / totalRequests;
        }
        
        /**
         * Calculate verification success rate as percentage
         * @return verification success rate (0-100)
         */
        public Double getVerificationSuccessRate() {
            long totalVerifications = (shieldVerificationSuccess != null ? shieldVerificationSuccess : 0) +
                                    (shieldVerificationFailed != null ? shieldVerificationFailed : 0);
            if (totalVerifications == 0) {
                return 0.0;
            }
            return (shieldVerificationSuccess != null ? shieldVerificationSuccess : 0) * 100.0 / totalVerifications;
        }
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class RiskCategoryMetrics {
        private Long fraudDetection;
        private Long velocityLimits;
        private Long behavioralAnomalies;
        private Long networkAnomalies;
        private Long deviceAnomalies;
        private Long temporalAnomalies;
        private Long userIdentityAnomalies;
        private Long crossCorrelation;
        private Long analyzeData;
        
        /**
         * Get total protection count across all risk categories
         * @return total protection count
         */
        public Long getTotalProtectionCount() {
            return (fraudDetection != null ? fraudDetection : 0) +
                   (velocityLimits != null ? velocityLimits : 0) +
                   (behavioralAnomalies != null ? behavioralAnomalies : 0) +
                   (networkAnomalies != null ? networkAnomalies : 0) +
                   (deviceAnomalies != null ? deviceAnomalies : 0) +
                   (temporalAnomalies != null ? temporalAnomalies : 0) +
                   (userIdentityAnomalies != null ? userIdentityAnomalies : 0) +
                   (crossCorrelation != null ? crossCorrelation : 0) +
                   (analyzeData != null ? analyzeData : 0);
        }
    }
}
