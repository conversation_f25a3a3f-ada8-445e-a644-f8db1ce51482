package vn.vinclub.shield.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import vn.vinclub.common.annotation.Profiler;
import vn.vinclub.common.util.JsonUtils;
import vn.vinclub.shield.dto.CustomerIdentity;
import vn.vinclub.shield.dto.DeviceFingerprint;
import vn.vinclub.shield.dto.ShieldEvaluationResult;
import vn.vinclub.shield.dto.request.ApiProtectRequest;
import vn.vinclub.shield.dto.response.ApiProtectResponse;
import vn.vinclub.shield.dto.response.ShieldStatisticsResponse;
import vn.vinclub.shield.enums.AppErrorCode;
import vn.vinclub.shield.enums.StatisticMetricType;
import vn.vinclub.shield.enums.StatisticTimePeriod;
import vn.vinclub.shield.service.ShieldEvaluationService;
import vn.vinclub.shield.service.ShieldService;
import vn.vinclub.shield.service.ShieldStatisticsService;
import vn.vinclub.shield.service.TrackingRequestService;
import vn.vinclub.shield.service.registry.TokenServiceRegistry;
import vn.vinclub.shield.util.TrackingContextUtils;

import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class ShieldServiceImpl implements ShieldService {
    private final TokenServiceRegistry tokenServiceRegistry;
    private final ShieldEvaluationService shieldEvaluationService;
    private final TrackingRequestService trackingRequestService;
    private final ShieldStatisticsService shieldStatisticsService;

    @Value("${shield.private-test.enabled:false}")
    private Boolean privateTestEnabled;

    @Value("${shield.private-test.phone-list:}")
    private String privateTestPhoneList;

    @Value("${shield.private-test.email-list:}")
    private String privateTestEmailList;

    @Value("${shield.private-test.customer-id-list:}")
    private String privateTestCustomerIdList;

    @Value("${shield.private-test.ip-list:}")
    private String privateTestIpList;

    @Value("${shield.private-test.device-id-list:}")
    private String privateTestDeviceIdList;

    @Profiler
    @Override
    public ApiProtectResponse protectApi(ApiProtectRequest request) {
        // Track total requests
        shieldStatisticsService.incrementMetric(StatisticMetricType.TOTAL_REQUESTS);

        // Validate request
        validateRequest(request);

        // check protect for private test data
        if (privateTestEnabled) {
            if (!isPrivateTestRequest(request)) {
                // Track evaluation pass for non-private test requests
                shieldStatisticsService.incrementMetric(StatisticMetricType.EVALUATION_PASS);
                return ApiProtectResponse.evaluate(ShieldEvaluationResult.pass());
            }
            log.info("Private test request: {}", JsonUtils.toString(request));
        }

        var evaluationData = request.toEvaluateData();

        // Check if the request has tokens to verify
        if (StringUtils.hasText(request.getShieldToken())) {
            if (CollectionUtils.isEmpty(request.getProtectTokens())) {
                log.error("Shield token provided but no tokens to verify");
                return ApiProtectResponse.verifyNotPass(AppErrorCode.SHIELD_TOKEN_INVALID);
            }

            var shieldTokenValidationResult = shieldEvaluationService.validateShieldToken(evaluationData, request.getShieldToken());

            if (!shieldTokenValidationResult.isValid()) {
                // Track shield verification failed
                shieldStatisticsService.incrementMetric(StatisticMetricType.SHIELD_VERIFICATION_FAILED);
                return ApiProtectResponse.verifyNotPass(AppErrorCode.SHIELD_TOKEN_INVALID);
            }

            log.info("Protecting request {} with tokens {}", JsonUtils.toString(request), request.getProtectTokens());
            for (var protectTokenData : request.getProtectTokens()) {
                var tokenService = tokenServiceRegistry.getService(protectTokenData.getKey());
                var verifyResult = tokenService.verifyToken(request.getAction(), request.getKey(), protectTokenData.getValue(), shieldTokenValidationResult.getIssuedAt(), shieldTokenValidationResult.getExpiresAt());
                if (!verifyResult.isValid()) {
                    // Track shield verification failed
                    shieldStatisticsService.incrementMetric(StatisticMetricType.SHIELD_VERIFICATION_FAILED);
                    return ApiProtectResponse.verifyNotPass(verifyResult.getFailureReason());
                }
            }
            // Put tracking data into bloom filter
            trackingRequestService.putTrackingRequestData(TrackingContextUtils.fromEvaluationData(evaluationData));
            // Track shield verification success
            shieldStatisticsService.incrementMetric(StatisticMetricType.SHIELD_VERIFICATION_SUCCESS);
            return ApiProtectResponse.verifyPass();
        }

        // Evaluate the request
        var evaluationResult = shieldEvaluationService.evaluate(evaluationData);
        if (evaluationResult.isPass()) {
            // Put tracking data into bloom filter
            trackingRequestService.putTrackingRequestData(TrackingContextUtils.fromEvaluationData(evaluationData));
            // Track evaluation pass
            shieldStatisticsService.incrementMetric(StatisticMetricType.EVALUATION_PASS);
        } else {
            // Track protection required
            shieldStatisticsService.incrementMetric(StatisticMetricType.PROTECTION_REQUIRED);
        }

        return ApiProtectResponse.evaluate(evaluationResult);
    }

    @Override
    @Profiler
    public ShieldStatisticsResponse getStatistics(StatisticTimePeriod timePeriod) {
        return shieldStatisticsService.getStatistics(timePeriod);
    }

    @Override
    @Profiler
    public Boolean resetStatistics(StatisticTimePeriod timePeriod) {
        return shieldStatisticsService.resetStatistics(timePeriod);
    }

    private void validateRequest(ApiProtectRequest request) {
        if (Objects.isNull(request)) {
            throw new IllegalArgumentException("Request is null");
        }

        if (Objects.isNull(request.getAction())) {
            throw new IllegalArgumentException("Action is required");
        }

        if (Objects.isNull(request.getKey())) {
            throw new IllegalArgumentException("Key is required");
        }

        if (Objects.isNull(request.getCustomerIdentity()) || !StringUtils.hasText(request.getCustomerIdentity().getCustomerId())
                && !StringUtils.hasText(request.getCustomerIdentity().getEmail())
                && !StringUtils.hasText(request.getCustomerIdentity().getPhone())) {
            throw new IllegalArgumentException("Customer identity is required");
        }

        if (Objects.isNull(request.getIpAddress())) {
            throw new IllegalArgumentException("IP address is required");
        }
    }

    private boolean isPrivateTestRequest(ApiProtectRequest request) {
        var customerId = Optional.ofNullable(request.getCustomerIdentity()).map(CustomerIdentity::getCustomerId);
        var email = Optional.ofNullable(request.getCustomerIdentity()).map(CustomerIdentity::getEmail);
        var phone = Optional.ofNullable(request.getCustomerIdentity()).map(CustomerIdentity::getPhone);
        var deviceId = Optional.ofNullable(request.getDeviceFingerprint()).map(DeviceFingerprint::getDeviceId);
        return customerId.map(privateTestCustomerIdList::contains).orElse(false)
                || email.map(privateTestEmailList::contains).orElse(false)
                || phone.map(privateTestPhoneList::contains).orElse(false)
                || deviceId.map(privateTestDeviceIdList::contains).orElse(false)
                || privateTestIpList.contains(request.getIpAddress());
    }
}
