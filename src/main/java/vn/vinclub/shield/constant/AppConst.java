package vn.vinclub.shield.constant;

import org.redisson.client.codec.Codec;
import org.redisson.client.codec.IntegerCodec;
import org.redisson.client.codec.LongCodec;
import org.redisson.codec.SerializationCodec;

public interface AppConst {
    String FIREBASE_APP_CHECK_TOKEN_SERVICE = "firebaseAppCheckTokenService";
    String FIREBASE_APP_CHECK_TOKEN_HEADER_KEY = "X-Firebase-AppCheck";

    String DEFAULT_LANGUAGE = "vi";
    String DEFAULT_COUNTRY = "VN";

    Codec SERIALIZATION_CODEC = new SerializationCodec();
    Codec LONG_CODEC = new LongCodec();
    Codec INTEGER_CODEC = new IntegerCodec();
}
