# spring config
spring.application.name=shield-service
server.port = 8080
server.servlet.context-path = /sh
spring.main.allow-bean-definition-overriding=true
spring.main.banner-mode=off

# server config
logging.config=classpath:logback-spring.xml
server.max-http-request-header-size=2MB
server.servlet.encoding.charset = UTF-8
server.servlet.session.tracking-modes = url

# REDDISON
redisson.config.address=${REDISSON_CONFIG_ADDRESS:redis://localhost:6379}
redisson.config.password=${REDISSON_CONFIG_PASSWORD:}
redisson.config.database=${REDISSON_CONFIG_DATABASE:0}
redisson.config.response.timeout=${REDISSON_CONFIG_RESPONSE_TIMEOUT:3000}
redisson.config.connection.timeout=${REDISSON_CONFIG_CONNECTION_TIMEOUT:3000}
redisson.config.connection.idle.time=${REDISSON_CONFIG_CONNECTION_IDLE_TIME:300000}
redisson.config.connection.keep-alive=${REDISSON_CONFIG_CONNECTION_KEEP_ALIVE:true}
redisson.config.connection.max=${REDISSON_CONFIG_CONNECTION_MAX:64}
redisson.config.connection.min=${REDISSON_CONFIG_CONNECTION_MIN:24}

# api docs
springdoc.api-docs.path = /api-docs
springdoc.swagger-ui.path = /swagger-ui.html

# rest logging
logging.level.io.swagger.models.parameters.AbstractSerializableParameter=error
vinclub.logging.level.client.RestTemplate = "DEBUG"

# Shield config
shield.phone.allowlist=${SHIELD_PHONE_ALLOW_LIST:}
shield.email.allowlist=${SHIELD_EMAIL_ALLOW_LIST:}
shield.customer-id.allowlist=${SHIELD_CUSTOMER_ID_ALLOW_LIST:}
shield.device-os.allowlist=${SHIELD_DEVICE_OS_ALLOW_LIST:}
shield.ip.allowlist=${SHIELD_IP_ALLOW_LIST:}
shield.ip.blocklist=${SHIELD_IP_BLOCK_LIST:}
shield.user-agent.blocklist=${SHIELD_USER_AGENT_BLOCK_LIST:bot,crawler,spider,scraper,http,java,python,curl,wget,postman,insomnia}
shield.action.protect-list=${SHIELD_ACTION_PROTECT_LIST:REGISTER_CHECK,REGISTER_CONFIRM,REGISTER_ONBOARDING,LOGIN,VBD_SDK_REQUEST_TOKEN}

# firebase app check
firebase.app-check.project.number=${FIREBASE_APP_CHECK_PROJECT_NUMBER:889370198281}
firebase.app-check.project.app_id=${FIREBASE_APP_CHECK_PROJECT_APP_ID:vinclub-development}
firebase.app-check.project.jwks=${FIREBASE_APP_CHECK_PROJECT_JWKS:https://firebaseappcheck.googleapis.com/v1/jwks}

# Shield Token Configuration
shield.token.issuer=${SHIELD_TOKEN_ISSUER:vss}
shield.token.audience=${SHIELD_TOKEN_AUDIENCE:vclub-service}
shield.token.expiration-minutes=${SHIELD_TOKEN_EXPIRATION_MINUTES:5}
shield.token.private-key=${SHIELD_TOKEN_PRIVATE_KEY:}
shield.token.public-key=${SHIELD_TOKEN_PUBLIC_KEY:}

# Shield Bloom Filter Configuration
shield.bloom.filter.global.size = ${SHIELD_BLOOM_GLOBAL_SIZE:1000000}
shield.bloom.filter.monthly.size = ${SHIELD_BLOOM_MONTHLY_SIZE:10000}
shield.bloom.filter.daily.size = ${SHIELD_BLOOM_DAILY_SIZE:1000}
shield.bloom.filter.false-positive-rate = ${SHIELD_BLOOM_FALSE_POSITIVE_RATE:0.01}

# Shield Analytics Configuration
shield.analytics.timezone=${SHIELD_TIMEZONE:GMT+7}

## Velocity detection configuration
shield.analytics.velocity.ttl-minutes=${SHIELD_VELOCITY_TTL_MINUTES:60}
shield.analytics.velocity.threshold=${SHIELD_VELOCITY_THRESHOLD:10}

## Rapid fire detection configuration
shield.analytics.rapid-fire.ttl-minutes=${SHIELD_RAPID_FIRE_TTL_MINUTES:5}
shield.analytics.rapid-fire.threshold=${SHIELD_RAPID_FIRE_THRESHOLD:5}

## New indicators detection configuration
shield.analytics.new-indicators.ttl-hours=${SHIELD_NEW_INDICATORS_TTL_HOURS:1}
shield.analytics.new-indicators.threshold=${SHIELD_NEW_INDICATORS_THRESHOLD:3}

## Off-hours activity detection configuration
shield.analytics.off-hours.start-hour=${SHIELD_OFF_HOURS_START:0}
shield.analytics.off-hours.end-hour=${SHIELD_OFF_HOURS_END:5}

## Automated behavior detection configuration
shield.analytics.automated-behavior.ttl-minutes=${SHIELD_AUTOMATED_BEHAVIOR_TTL_MINUTES:30}
shield.analytics.automated-behavior.analysis-window-minutes=${SHIELD_AUTOMATED_BEHAVIOR_WINDOW_MINUTES:5}
shield.analytics.automated-behavior.threshold=${SHIELD_AUTOMATED_BEHAVIOR_THRESHOLD:15}

## Unusual activity detection configuration
shield.analytics.unusual-activity.ttl-hours=${SHIELD_UNUSUAL_ACTIVITY_TTL_HOURS:24}
shield.analytics.unusual-activity.threshold=${SHIELD_UNUSUAL_ACTIVITY_THRESHOLD:12}
