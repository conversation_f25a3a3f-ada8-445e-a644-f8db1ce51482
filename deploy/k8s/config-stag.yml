apiVersion: v1
kind: ConfigMap
metadata:
  name: vclub-shield
  namespace: vinclub-backend-stag
data:
  JAVA_OPTS: "-Xmx1024m -Dlog4j.formatMsgNoLookups=true -javaagent:/usr/app/elastic-apm-agent.jar -Delastic.apm.service_name=vclub-shield-service -Delastic.apm.application_packages=com.vinloyalty,vn.vinclub -Delastic.apm.server_url=https://stag-apm.vinhomes.vn -Delastic.apm.secret_token=6NZOW0qPVsfVIdKv1oVZa0Gq -Delastic.apm.environment=stag"

  # PROFILER
  PROFILER_KEY: "VinClub@123!"

  #  REDIS CONFIG
  REDISSON_CONFIG_HOST: "vhm-vinclub-stag-redis-azc.05cno1.0001.apse1.cache.amazonaws.com"
  REDISSON_CONFIG_PORT: "6379"
  REDISSON_CONFIG_ADDRESS: 'redis://vhm-vinclub-stag-redis-azc.05cno1.0001.apse1.cache.amazonaws.com:6379'
  REDISSON_CONFIG_PASSWORD: ''
  REDISSON_CONFIG_DATABASE: '2'
  REDISSON_CONFIG_RESPONSE_TIMEOUT: '3000'
  REDISSON_CONFIG_CONNECTION_TIMEOUT: '3000'
  REDISSON_CONFIG_CONNECTION_IDLE_TIME: '300000'
  REDISSON_CONFIG_CONNECTION_KEEP_ALIVE: 'true'
  REDISSON_CONFIG_CONNECTION_MAX: '64'
  REDISSON_CONFIG_CONNECTION_MIN: '4'
  REDISSON_CHANNEL_CHANGE: "CHANNEL.UPDATE"

  # SHIELD CONFIG
  SHIELD_ACTION_PROTECT_LIST: "REGISTER_CHECK,REGISTER_CONFIRM,REGISTER_ONBOARDING,LOGIN,VBD_SDK_REQUEST_TOKEN"
  SHIELD_PHONE_ALLOW_LIST: "+84909000004,+84348272727"
  SHIELD_EMAIL_ALLOW_LIST: "<EMAIL>"
  SHIELD_CUSTOMER_ID_ALLOW_LIST: "14616723605811200,41550447965184"
  SHIELD_DEVICE_OS_ALLOW_LIST: "android"
  SHIELD_IP_ALLOW_LIST: "**********,**************,************"
  SHIELD_IP_BLOCK_LIST: ""
  SHIELD_USER_AGENT_BLOCK_LIST: "bot,crawler,spider,scraper,http,java,python,curl,wget,postman,insomnia"

  # FIREBASE APP CHECK CONFIG
  FIREBASE_APP_CHECK_PROJECT_NUMBER: "889370198281"
  FIREBASE_APP_CHECK_PROJECT_APP_ID: "vinclub-development"
  FIREBASE_APP_CHECK_PROJECT_JWKS: "https://firebaseappcheck.googleapis.com/v1/jwks"

  # SHIELD BLOOM FILTER CONFIG
  SHIELD_BLOOM_FILTER_GLOBAL_SIZE: "2000000"
  SHIELD_BLOOM_FILTER_MONTHLY_SIZE: "1000000"
  SHIELD_BLOOM_FILTER_DAILY_SIZE: "100000"
  SHIELD_BLOOM_FILTER_FALSE_POSITIVE_RATE: "0.01"

  # SHIELD ANALYTICS CONFIG
  SHIELD_TIMEZONE: "GMT+7"
  SHIELD_VELOCITY_TTL_MINUTES: "60"
  SHIELD_VELOCITY_THRESHOLD: "10"
  SHIELD_RAPID_FIRE_TTL_MINUTES: "5"
  SHIELD_RAPID_FIRE_THRESHOLD: "5"
  SHIELD_NEW_INDICATORS_TTL_HOURS: "1"
  SHIELD_NEW_INDICATORS_THRESHOLD: "3"
  SHIELD_OFF_HOURS_START: "0"
  SHIELD_OFF_HOURS_END: "5"
  SHIELD_AUTOMATED_BEHAVIOR_TTL_MINUTES: "30"
  SHIELD_AUTOMATED_BEHAVIOR_WINDOW_MINUTES: "5"
  SHIELD_AUTOMATED_BEHAVIOR_THRESHOLD: "15"
  SHIELD_UNUSUAL_ACTIVITY_TTL_HOURS: "24"
  SHIELD_UNUSUAL_ACTIVITY_THRESHOLD: "12"

  # SHIELD PRIVATE TEST CONFIG
  SHIELD_PRIVATE_TEST_ENABLED: "false"
  SHIELD_PRIVATE_TEST_PHONE_LIST: ""
  SHIELD_PRIVATE_TEST_EMAIL_LIST: ""
  SHIELD_PRIVATE_TEST_CUSTOMER_ID_LIST: ""
  SHIELD_PRIVATE_TEST_IP_LIST: ""
  SHIELD_PRIVATE_TEST_DEVICE_ID_LIST: ""

  # SHIELD TOKEN CONFIG
  SHIELD_TOKEN_ISSUER: "vss"
  SHIELD_TOKEN_AUDIENCE: "vclub-service"
  SHIELD_TOKEN_EXPIRATION_MINUTES: "5"
  SHIELD_TOKEN_PRIVATE_KEY: |
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

  SHIELD_TOKEN_PUBLIC_KEY: |
    -----BEGIN PUBLIC KEY-----
    MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA4FezKfRHwNrLTOV2Nkjh
    WAr1kdjjWU7EN9/esO9NeG88SqWcjUf/2zg0b88m2cnKlwnXMGTNzBHdnEBmBOKM
    2oKQBdCyCc58TmVxDx+JVS73Qrbez3DfbarN2et9jbzF/BxTubZ8ki2j6zpobjCV
    Smc4h8Q0Wbn1E0cXZL4PO4tIxbSndIE42X9x40SJnBqKNbtX1dPDT1HkEknIMisP
    e4u+VQvKsXjUkj1yexYoiQcnb+LQimajOKhrgqECGPkrdYghilruXh+cC5umRwb5
    OQtfHQGgxR3HELKrcGqaF4d6px8Wu10G77lj8L6/vXpXqLPDqrYfbuxZLaVAu7Rx
    rwIDAQAB
    -----END PUBLIC KEY-----
    
